/* eslint-disable @typescript-eslint/no-explicit-any */
import { prisma } from './prisma';
import type { Model, ModelAnalysis, Prompt, ModelState } from '../generated/prisma';

export interface ModelData {
  fileName: string;
  filePath?: string;
  fileSize?: number;
}

export interface AnalysisData {
  analysisType: string;
  result: any;
  metadata?: any;
}

export interface PromptData {
  userPrompt: string;
  aiResponse?: string;
  promptType: string;
  status?: string;
  metadata?: any;
}

export interface ObjectState {
  objectId: string;
  objectName?: string;
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
  scale: { x: number; y: number; z: number };
  visible?: boolean;
  selected?: boolean;
  properties?: any;
}

export class ModelService {
  // Создание или получение модели по имени файла с анализом
  static async getOrCreateModel(
    modelData: ModelData,
  ): Promise<Model & { latestAnalysis?: ModelAnalysis | null }> {
    const existingModel = await prisma.model.findUnique({
      where: { fileName: modelData.fileName },
      include: {
        analysis: true,
      },
    });

    if (existingModel) {
      return {
        ...existingModel,
        latestAnalysis: existingModel.analysis || null,
      };
    }

    const newModel = await prisma.model.create({
      data: {
        fileName: modelData.fileName,
        filePath: modelData.filePath,
        fileSize: modelData.fileSize,
      },
    });

    return {
      ...newModel,
      latestAnalysis: null,
    };
  }

  // Получение модели по имени файла
  static async getModelByFileName(fileName: string): Promise<Model | null> {
    return await prisma.model.findUnique({
      where: { fileName },
      include: {
        analysis: true,
        prompts: {
          orderBy: { createdAt: 'desc' },
        },
        modelStates: true,
      },
    });
  }

  // Сохранение результата анализа
  static async saveAnalysis(
    modelId: string,
    analysisData: AnalysisData,
  ): Promise<ModelAnalysis> {
    // Используем upsert для создания или обновления единственного анализа
    return await prisma.modelAnalysis.upsert({
      where: { modelId },
      update: {
        analysisType: analysisData.analysisType,
        result: JSON.stringify(analysisData.result),
        metadata: analysisData.metadata
          ? JSON.stringify(analysisData.metadata)
          : null,
      },
      create: {
        modelId,
        analysisType: analysisData.analysisType,
        result: JSON.stringify(analysisData.result),
        metadata: analysisData.metadata
          ? JSON.stringify(analysisData.metadata)
          : null,
      },
    });
  }

  // Сохранение промпта
  static async savePrompt(
    modelId: string,
    promptData: PromptData,
  ): Promise<Prompt> {
    return await prisma.prompt.create({
      data: {
        modelId,
        userPrompt: promptData.userPrompt,
        aiResponse: promptData.aiResponse,
        promptType: promptData.promptType,
        status: promptData.status || 'pending',
        metadata: promptData.metadata
          ? JSON.stringify(promptData.metadata)
          : null,
      },
    });
  }

  // Обновление промпта с ответом AI
  static async updatePromptWithResponse(
    promptId: string,
    aiResponse: string,
    status: string = 'completed',
  ): Promise<Prompt> {
    return await prisma.prompt.update({
      where: { id: promptId },
      data: {
        aiResponse,
        status,
        updatedAt: new Date(),
      },
    });
  }

  // Получение истории промптов для модели
  static async getPromptHistory(
    modelId: string,
    limit: number = 10,
  ): Promise<Prompt[]> {
    return await prisma.prompt.findMany({
      where: { modelId },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });
  }

  // Сохранение состояния объектов модели
  static async saveModelState(
    modelId: string,
    objectStates: ObjectState[],
  ): Promise<void> {
    // Удаляем старые состояния для этой модели
    await prisma.modelState.deleteMany({
      where: { modelId },
    });

    // Создаем новые состояния
    await prisma.modelState.createMany({
      data: objectStates.map(state => ({
        modelId,
        objectId: state.objectId,
        objectName: state.objectName,
        position: JSON.stringify(state.position),
        rotation: JSON.stringify(state.rotation),
        scale: JSON.stringify(state.scale),
        visible: state.visible ?? true,
        selected: state.selected ?? false,
        properties: state.properties ? JSON.stringify(state.properties) : null,
      })),
    });
  }

  // Получение состояния объектов модели
  static async getModelState(modelId: string): Promise<ObjectState[]> {
    const states = await prisma.modelState.findMany({
      where: { modelId },
    });

    return states.map(state => ({
      objectId: state.objectId,
      objectName: state.objectName || undefined,
      position: JSON.parse(state.position),
      rotation: JSON.parse(state.rotation),
      scale: JSON.parse(state.scale),
      visible: state.visible,
      selected: state.selected,
      properties: state.properties ? JSON.parse(state.properties) : undefined,
    }));
  }

  // Обновление состояния конкретного объекта
  static async updateObjectState(
    modelId: string,
    objectState: ObjectState,
  ): Promise<ModelState> {
    return await prisma.modelState.upsert({
      where: {
        modelId_objectId: {
          modelId,
          objectId: objectState.objectId,
        },
      },
      update: {
        objectName: objectState.objectName,
        position: JSON.stringify(objectState.position),
        rotation: JSON.stringify(objectState.rotation),
        scale: JSON.stringify(objectState.scale),
        visible: objectState.visible ?? true,
        selected: objectState.selected ?? false,
        properties: objectState.properties
          ? JSON.stringify(objectState.properties)
          : null,
        updatedAt: new Date(),
      },
      create: {
        modelId,
        objectId: objectState.objectId,
        objectName: objectState.objectName,
        position: JSON.stringify(objectState.position),
        rotation: JSON.stringify(objectState.rotation),
        scale: JSON.stringify(objectState.scale),
        visible: objectState.visible ?? true,
        selected: objectState.selected ?? false,
        properties: objectState.properties
          ? JSON.stringify(objectState.properties)
          : null,
      },
    });
  }

  // Получение всех моделей
  static async getAllModels(): Promise<Model[]> {
    return await prisma.model.findMany({
      orderBy: { updatedAt: 'desc' },
      include: {
        analysis: true,
        _count: {
          select: {
            prompts: true,
            modelStates: true,
          },
        },
      },
    });
  }

  // Удаление модели и всех связанных данных
  static async deleteModel(modelId: string): Promise<void> {
    await prisma.model.delete({
      where: { id: modelId },
    });
  }

  // Получение анализа модели
  static async getModelAnalysis(
    modelId: string,
  ): Promise<ModelAnalysis | null> {
    return await prisma.modelAnalysis.findUnique({
      where: { modelId },
    });
  }
}
